

// Initialize Lottie animation safely
let loadingAnimation;
try {
  const animationContainer = document.getElementById('loading-animation');
  if (animationContainer) {
    loadingAnimation = lottie.loadAnimation({
      container: animationContainer,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      path: 'assess_loading.json',
      initialSegment: [0, 60] // Play first 60 frames for faster animation cycle
    });
    // Set animation speed to 0.5x for smoother appearance
    loadingAnimation.setSpeed(0.5);
  }
} catch (error) {
  // Silently handle Lottie animation errors
}

const firebaseConfig = {
    apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
    authDomain: "barefoot-elearning-app.firebaseapp.com",
    projectId: "barefoot-elearning-app",
    databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
    storageBucket: "barefoot-elearning-app.appspot.com",
    messagingSenderId: "170819735788",
    appId: "1:170819735788:web:223af318437eb5d947d5c9"
  };

  firebase.initializeApp(firebaseConfig);
  const db = firebase.firestore();

  // ENHANCEMENT: Comprehensive admin login tracking function
  async function trackAdminLogin(user) {
    console.log('DEBUG: trackAdminLogin function started for:', user.email);
    try {
      const sessionId = generateSessionId();
      const loginTimestamp = firebase.firestore.FieldValue.serverTimestamp();

      // Create session data for client-side tracking
      const sessionData = {
        loginTime: loginTimestamp,
        timestamp: loginTimestamp, // For compatibility with tracking collection
        userAgent: navigator.userAgent,
        sessionId: sessionId,
        ipAddress: 'client-side', // Will be updated by server
        loginMethod: 'email_password',
        loginPage: 'login.html',
        clientTracked: true
      };

      // First, do client-side tracking for immediate update
      const adminRef = db.collection('Admins').doc(user.email);
      await db.runTransaction(async (transaction) => {
        const adminDoc = await transaction.get(adminRef);

        if (adminDoc.exists) {
          const adminData = adminDoc.data();
          const updates = {
            lastLoginTime: loginTimestamp,
            lastLoginTimestamp: loginTimestamp, // Keep both for compatibility
            lastModified: loginTimestamp
          };

          // Add to login sessions array (keep last 100 sessions)
          let loginSessions = adminData.loginSessions || [];
          loginSessions.unshift(sessionData);

          if (loginSessions.length > 100) {
            loginSessions = loginSessions.slice(0, 100);
          }

          updates.loginSessions = loginSessions;
          updates.totalLogins = (adminData.totalLogins || 0) + 1;

          transaction.update(adminRef, updates);
        }
      });

      // Then, send to server for comprehensive tracking with IP address
      console.log('DEBUG: Sending server tracking request for:', user.email);
      try {
        const response = await fetch('/track-admin-login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: user.email,
            sessionId: sessionId,
            userAgent: navigator.userAgent,
            loginMethod: 'email_password',
            loginPage: 'login.html'
          })
        });
        const result = await response.json();
        console.log('DEBUG: Server tracking response:', result);
      } catch (serverTrackingError) {
        console.error('DEBUG: Server tracking error:', serverTrackingError);
        // Don't block login if server tracking fails
      }

    } catch (error) {
      // Silently handle tracking errors to not disrupt login flow
    }
  }

  // Generate unique session ID
  function generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

// Wrap main logic in DOMContentLoaded to ensure elements are available
document.addEventListener('DOMContentLoaded', function() {
  // Get references to the email and password input fields
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  //get a ref to the loading overlay
  const loadingOverlay = document.getElementById('loading-overlay');

  // Get references to the email and password error elements
  const emailError = document.getElementById('emailError');
  const passwordError = document.getElementById('passwordError');

  // Get references to the form, sign-in button and forgot password
  const loginForm = document.getElementById('login-form');
  const signInButton = document.querySelector('button[type="submit"]');
  const forgotPasswordLink = document.getElementById('forgotPassword');

// Add form submit event listener to prevent default submission
if (loginForm) {
  loginForm.addEventListener('submit', (e) => {
    e.preventDefault(); // Prevent the default form submission
    handleSignIn();
  });
}

// Add a click event listener to the sign-in button as backup
if (signInButton) {
  signInButton.addEventListener('click', (e) => {
    e.preventDefault(); // Prevent the default form submission
    handleSignIn();
  });
}

// Extract sign-in logic into a separate function
function handleSignIn() {
  const email = emailInput.value;
  const password = passwordInput.value;

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    showError(emailInput, emailError, 'Please enter a valid email address.');
    return;
  }

  //show loading overlay
  if (window.LoadingOverlay) {
    window.LoadingOverlay.show('login', 'Signing in...');
  } else {
    loadingOverlay.style.display = 'flex';
  }

  // Sign in the user with email and password
  console.log('DEBUG: Attempting Firebase sign in for:', email);
  firebase.auth().signInWithEmailAndPassword(email, password)
  .then(async (userCredential) => {
    // User signed in successfully
    const user = userCredential.user;
    console.log('DEBUG: Firebase sign in successful for:', user.email);

    // ENHANCEMENT: Track admin login for activity monitoring
    console.log('DEBUG: About to call trackAdminLogin for:', user.email);
    try {
      await trackAdminLogin(user);
      console.log('DEBUG: trackAdminLogin completed successfully');
    } catch (trackingError) {
      console.error('DEBUG: trackAdminLogin failed:', trackingError);
      // Don't block login flow if tracking fails
    }

    // Check for redirect parameters
    const urlParams = new URLSearchParams(window.location.search);
    const redirect = urlParams.get('redirect');

    if (redirect) {
      // If coming from success or cancel page, redirect to main with parameter
      window.location.href = `main.html?from=${redirect}`;
    } else {
      // Removed email verification check - proceed directly to dashboard
      window.location.href = 'main.html';
    }
  })
  .catch((error) => {
    // Handle errors
    const errorCode = error.code;
    const errorMessage = error.message;

    // Hide loading overlay first
    if (window.LoadingOverlay) {
      window.LoadingOverlay.hide();
    } else {
      loadingOverlay.style.display = 'none';
    }

    // Handle the specific INVALID_LOGIN_CREDENTIALS error
    if (errorMessage.includes('INVALID_LOGIN_CREDENTIALS')) {
      // Create a more user-friendly container for the error
      createFriendlyErrorMessage(
        "The email or password you entered is incorrect.",
        "Please check your credentials and try again."
      );

      // Don't show individual field errors - focus on password field for better UX
      passwordInput.focus();
      return;
    }

    switch(errorCode) {
      case 'auth/wrong-password':
        createFriendlyErrorMessage("Incorrect Password", "The password you entered is incorrect. Please try again.");
        break;
      case 'auth/user-not-found':
        createFriendlyErrorMessage("Account Not Found", "The email address you entered is not registered. Please sign up or check your email address.");
        break;
      case 'auth/invalid-email':
        createFriendlyErrorMessage("Invalid Email", "The email address you entered is not valid. Please enter a valid email address.");
        break;
      case 'auth/user-disabled':
        createFriendlyErrorMessage("Account Disabled", "This account has been disabled. Please contact support for assistance.");
        break;
      case 'auth/too-many-requests':
        createFriendlyErrorMessage("Too Many Attempts", "Access to this account has been temporarily disabled due to many failed login attempts. You can restore it by resetting your password or try again later.");
        break;
      case 'auth/account-exists-with-different-credential':
        createFriendlyErrorMessage("Login Method Issue", "An account already exists with this email but with a different sign-in method. Try another sign-in method.");
        break;
      case 'auth/operation-not-allowed':
        createFriendlyErrorMessage("Login Error", "This type of sign-in is not allowed. Please contact support.");
        break;
      default:
        createFriendlyErrorMessage("Authentication Error", "Unable to sign in. Please check your credentials and try again.");
    }
  })
  .finally(() => {
    // Hide the loading overlay
    if (window.LoadingOverlay) {
      window.LoadingOverlay.hide();
    } else {
      loadingOverlay.style.display = 'none';
    }
  });
}

// Function to show the error message with a shake animation
function showError(inputElement, errorElement, errorMessage) {
  errorElement.textContent = errorMessage;
  errorElement.classList.remove('hidden');

  // Enhanced styling for the error message
  Object.assign(errorElement.style, {
    color: '#DC2626',
    fontSize: '0.8rem',
    marginTop: '0.35rem',
    fontWeight: '500',
    display: 'flex',
    alignItems: 'center',
    animation: 'fadeIn 0.3s ease-in'
  });

  // Add a small warning icon before the text
  if (!errorElement.querySelector('.error-icon')) {
    const errorIcon = document.createElement('span');
    errorIcon.className = 'error-icon';
    errorIcon.innerHTML = '⚠️';
    errorIcon.style.marginRight = '4px';
    errorIcon.style.fontSize = '0.75rem';
    errorElement.prepend(errorIcon);
  }

  // Style the input to show it's in error state
  inputElement.classList.add('border-red-500');
  inputElement.style.backgroundColor = '#FEF2F2';
  inputElement.style.borderWidth = '1px';

  // Add shake animation
  inputElement.classList.add('animate-shake');
  setTimeout(() => {
    inputElement.classList.remove('animate-shake');
  }, 500);

  // Add animation styles if they don't exist
  if (!document.querySelector('#error-fade-in-style')) {
    const styleEl = document.createElement('style');
    styleEl.id = 'error-fade-in-style';
    styleEl.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-5px); }
        to { opacity: 1; transform: translateY(0); }
      }
    `;
    document.head.appendChild(styleEl);
  }

  // Focus on the input for better UX
  inputElement.focus();
}

// Function to hide the error message
function hideError(inputElement, errorElement) {
  // Hide the individual error if it exists
  if (errorElement) {
    errorElement.classList.add('hidden');
    errorElement.style.display = 'none';
  }

  // Remove error styling from input
  if (inputElement) {
    inputElement.classList.remove('border-red-500');
    inputElement.style.backgroundColor = '';
  }

  // Also remove any friendly error containers when user starts fixing the issue
  const friendlyError = document.querySelector('.friendly-error-container');
  if (friendlyError) {
    friendlyError.style.opacity = '0';
    friendlyError.style.transition = 'opacity 0.3s ease';
    setTimeout(() => friendlyError.remove(), 300);
  }
}

// Add input event listeners to hide the error message when the user starts typing
emailInput.addEventListener('input', () => {
  hideError(emailInput, emailError);
});

passwordInput.addEventListener('input', () => {
  hideError(passwordInput, passwordError);
});

// Enhance the forgot password functionality with better error handling
forgotPasswordLink.addEventListener('click', (e) => {
  e.preventDefault();
  const emailInput = document.getElementById('email');
  const email = emailInput.value.trim();

  if (email) {
    // Show the loading overlay
    if (window.LoadingOverlay) {
      window.LoadingOverlay.show('login', 'Sending password reset email...');
    } else {
      loadingOverlay.style.display = 'flex';
    }

    firebase.auth().sendPasswordResetEmail(email)
      .then(() => {
        // Create a more noticeable success message
        const successDiv = document.createElement('div');
        successDiv.className = 'py-2 px-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg';
        successDiv.textContent = 'Password reset email sent! Please check your inbox and spam folder.';

        // Insert before the form
        const form = document.querySelector('form');
        form.parentNode.insertBefore(successDiv, form);

        // Remove after 10 seconds
        setTimeout(() => {
          if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
          }
        }, 10000);
      })
      .catch((error) => {
        const errorCode = error.code;

        switch(errorCode) {
          case 'auth/invalid-email':
            showError(emailInput, emailError, 'The email address you entered is not valid.');
            break;
          case 'auth/user-not-found':
            showError(emailInput, emailError, 'No account exists with this email address.');
            break;
          case 'auth/too-many-requests':
            showError(emailInput, emailError, 'Too many requests. Please try again later.');
            break;
          default:
            showError(emailInput, emailError, 'Failed to send password reset email. Please try again.');
        }
      })
      .finally(() => {
        // Hide the loading overlay
        if (window.LoadingOverlay) {
          window.LoadingOverlay.hide();
        } else {
          loadingOverlay.style.display = 'none';
        }
      });
  } else {
    // If no email in the input field, show a prompt
    const promptEmail = prompt('Enter your email address to reset your password:');
    if (promptEmail) {
      // Show the loading overlay
      if (window.LoadingOverlay) {
        window.LoadingOverlay.show('login', 'Sending password reset email...');
      } else {
        loadingOverlay.style.display = 'flex';
      }

      firebase.auth().sendPasswordResetEmail(promptEmail)
        .then(() => {
          alert('Password reset email sent. Please check your inbox and spam folder.');
        })
        .catch((error) => {
          const errorCode = error.code;

          switch(errorCode) {
            case 'auth/invalid-email':
              alert('The email address you entered is not valid.');
              break;
            case 'auth/user-not-found':
              alert('No account exists with this email address.');
              break;
            default:
              alert('Failed to send password reset email. Please try again.');
          }
        })
        .finally(() => {
          // Hide the loading overlay
          if (window.LoadingOverlay) {
            window.LoadingOverlay.hide();
          } else {
            loadingOverlay.style.display = 'none';
          }
        });
    }
  }
});

// New function to create a friendly error message box
function createFriendlyErrorMessage(mainMessage, subMessage = "") {
  // Remove any existing error messages
  const existingError = document.querySelector('.friendly-error-container');
  if (existingError) {
    existingError.remove();
  }

  const form = document.querySelector('form');
  const errorContainer = document.createElement('div');
  errorContainer.className = 'friendly-error-container';

  // Apply styling directly for immediate effect
  Object.assign(errorContainer.style, {
    backgroundColor: '#FEE2E2',
    borderLeft: '4px solid #DC2626',
    borderRadius: '4px',
    padding: '12px 16px',
    marginBottom: '20px',
    color: '#991B1B',
    fontSize: '0.9rem',
    animation: 'shake-horizontal 0.8s cubic-bezier(.36,.07,.19,.97) both',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
  });

  // Create the content
  const mainText = document.createElement('p');
  mainText.style.fontWeight = '600';
  mainText.style.marginBottom = subMessage ? '4px' : '0';
  mainText.textContent = mainMessage;

  errorContainer.appendChild(mainText);

  if (subMessage) {
    const subText = document.createElement('p');
    subText.style.fontSize = '0.85rem';
    subText.style.opacity = '0.9';
    subText.textContent = subMessage;
    errorContainer.appendChild(subText);
  }

  // Add a close button
  const closeButton = document.createElement('button');
  closeButton.innerHTML = '&times;';
  closeButton.style.position = 'absolute';
  closeButton.style.right = '8px';
  closeButton.style.top = '8px';
  closeButton.style.background = 'none';
  closeButton.style.border = 'none';
  closeButton.style.fontSize = '1.2rem';
  closeButton.style.cursor = 'pointer';
  closeButton.style.color = '#991B1B';
  closeButton.onclick = () => errorContainer.remove();

  errorContainer.style.position = 'relative';
  errorContainer.appendChild(closeButton);

  // Insert at the top of the form
  form.parentNode.insertBefore(errorContainer, form);

  // Add keyframe animation for shake effect
  if (!document.querySelector('#error-animation-style')) {
    const styleEl = document.createElement('style');
    styleEl.id = 'error-animation-style';
    styleEl.textContent = `
      @keyframes shake-horizontal {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
      }
    `;
    document.head.appendChild(styleEl);
  }

  // Automatically remove after 10 seconds
  setTimeout(() => {
    if (document.body.contains(errorContainer)) {
      errorContainer.style.opacity = '0';
      errorContainer.style.transition = 'opacity 0.5s ease';
      setTimeout(() => {
        if (document.body.contains(errorContainer)) {
          errorContainer.remove();
        }
      }, 500);
    }
  }, 10000);
}

}); // End of DOMContentLoaded