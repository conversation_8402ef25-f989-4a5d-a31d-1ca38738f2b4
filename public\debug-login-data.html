<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login Data</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 300px; }
    </style>
</head>
<body>
    <h2>Debug Login Tracking Data</h2>
    
    <div class="section">
        <h3>Check Admin Login Data</h3>
        <input type="email" id="admin-email" placeholder="Enter admin email to check" value="">
        <button onclick="checkAdminData()">Check Admin Data</button>
        <div id="admin-result" class="result"></div>
    </div>

    <div class="section">
        <h3>Check Login Tracking Collection</h3>
        <button onclick="checkTrackingData()">Check Tracking Collection</button>
        <div id="tracking-result" class="result"></div>
    </div>

    <div class="section">
        <h3>Test Login Tracking Function</h3>
        <button onclick="testTrackingFunction()">Test Tracking Function</button>
        <div id="test-result" class="result"></div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>

    <script>
        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        function generateSessionId() {
            return 'debug_session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
        }

        async function checkAdminData() {
            const email = document.getElementById('admin-email').value;
            const resultDiv = document.getElementById('admin-result');
            
            if (!email) {
                resultDiv.textContent = 'Please enter an email address';
                return;
            }

            resultDiv.textContent = 'Checking admin data...';

            try {
                const adminRef = db.collection('Admins').doc(email);
                const adminDoc = await adminRef.get();

                if (adminDoc.exists) {
                    const data = adminDoc.data();
                    const loginData = {
                        email: email,
                        lastLoginTime: data.lastLoginTime ? (data.lastLoginTime.toDate ? data.lastLoginTime.toDate().toISOString() : data.lastLoginTime) : 'None',
                        lastLoginTimestamp: data.lastLoginTimestamp ? (data.lastLoginTimestamp.toDate ? data.lastLoginTimestamp.toDate().toISOString() : data.lastLoginTimestamp) : 'None',
                        totalLogins: data.totalLogins || 0,
                        loginSessionsCount: data.loginSessions ? data.loginSessions.length : 0,
                        recentSessions: data.loginSessions ? data.loginSessions.slice(0, 3).map(session => ({
                            loginTime: session.loginTime ? (session.loginTime.toDate ? session.loginTime.toDate().toISOString() : session.loginTime) : 'None',
                            sessionId: session.sessionId,
                            userAgent: session.userAgent ? session.userAgent.substring(0, 50) + '...' : 'None',
                            ipAddress: session.ipAddress
                        })) : []
                    };
                    resultDiv.textContent = JSON.stringify(loginData, null, 2);
                } else {
                    resultDiv.textContent = `Admin document not found for: ${email}`;
                }
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function checkTrackingData() {
            const email = document.getElementById('admin-email').value;
            const resultDiv = document.getElementById('tracking-result');
            
            if (!email) {
                resultDiv.textContent = 'Please enter an email address first';
                return;
            }

            resultDiv.textContent = 'Checking tracking collection...';

            try {
                const trackingRef = db.collection('AdminLoginTracking').doc(email);
                const trackingDoc = await trackingRef.get();

                if (trackingDoc.exists) {
                    const data = trackingDoc.data();
                    const trackingData = {
                        email: data.email,
                        lastLogin: data.lastLogin ? (data.lastLogin.toDate ? data.lastLogin.toDate().toISOString() : data.lastLogin) : 'None',
                        totalLogins: data.totalLogins || 0,
                        loginHistoryCount: data.loginHistory ? data.loginHistory.length : 0,
                        recentSessionsCount: data.recentSessions ? data.recentSessions.length : 0
                    };
                    resultDiv.textContent = JSON.stringify(trackingData, null, 2);
                } else {
                    resultDiv.textContent = `Tracking document not found for: ${email}`;
                }
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function testTrackingFunction() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.textContent = 'Testing tracking function...';

            try {
                const testEmail = '<EMAIL>';
                const sessionId = generateSessionId();
                
                // Test server endpoint
                const response = await fetch('/track-admin-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: testEmail,
                        sessionId: sessionId,
                        userAgent: navigator.userAgent,
                        loginMethod: 'debug_test',
                        loginPage: 'debug-login-data.html'
                    })
                });

                const result = await response.json();
                resultDiv.textContent = `Server response: ${JSON.stringify(result, null, 2)}`;
                
                // Now check if data was stored
                setTimeout(async () => {
                    try {
                        const adminRef = db.collection('Admins').doc(testEmail);
                        const adminDoc = await adminRef.get();
                        
                        if (adminDoc.exists) {
                            const data = adminDoc.data();
                            resultDiv.textContent += `\n\nStored data check:\nTotal logins: ${data.totalLogins || 0}\nLogin sessions: ${data.loginSessions ? data.loginSessions.length : 0}`;
                        } else {
                            resultDiv.textContent += '\n\nNo admin document found after tracking';
                        }
                    } catch (error) {
                        resultDiv.textContent += `\n\nError checking stored data: ${error.message}`;
                    }
                }, 2000);

            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
