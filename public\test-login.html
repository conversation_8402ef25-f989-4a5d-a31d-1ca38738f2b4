<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 400px; margin: 0 auto; }
        input, button { display: block; width: 100%; margin: 10px 0; padding: 10px; }
        button { background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .debug { background: #f8f9fa; padding: 10px; margin: 10px 0; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Login Test Page</h2>
        <div class="debug" id="debug-output">Debug output will appear here...</div>
        
        <form id="test-login-form">
            <input type="email" id="test-email" placeholder="Email" value="<EMAIL>">
            <input type="password" id="test-password" placeholder="Password" value="testpassword">
            <button type="submit" id="test-submit">Test Sign In</button>
        </form>
        
        <button onclick="testFirebase()">Test Firebase Connection</button>
        <button onclick="testElements()">Test Element Selection</button>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>

    <script>
        const debugOutput = document.getElementById('debug-output');
        
        function log(message) {
            console.log(message);
            debugOutput.innerHTML += '<br>' + message;
        }
        
        log('Test page loaded');
        
        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };
        
        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            log('Firebase initialized successfully');
        } catch (error) {
            log('Firebase initialization error: ' + error.message);
        }
        
        function testFirebase() {
            try {
                log('Testing Firebase Auth...');
                log('Firebase Auth available: ' + !!firebase.auth);
                log('Firebase Firestore available: ' + !!firebase.firestore);
            } catch (error) {
                log('Firebase test error: ' + error.message);
            }
        }
        
        function testElements() {
            const form = document.getElementById('test-login-form');
            const email = document.getElementById('test-email');
            const password = document.getElementById('test-password');
            const submit = document.getElementById('test-submit');
            
            log('Form element: ' + !!form);
            log('Email element: ' + !!email);
            log('Password element: ' + !!password);
            log('Submit element: ' + !!submit);
        }
        
        // Test form submission
        document.getElementById('test-login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            log('Form submitted!');
            
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            log('Email: ' + email);
            log('Password length: ' + password.length);
            
            // Test Firebase auth
            firebase.auth().signInWithEmailAndPassword(email, password)
                .then((userCredential) => {
                    log('Login successful: ' + userCredential.user.email);
                })
                .catch((error) => {
                    log('Login error: ' + error.message);
                });
        });
        
        log('Test page setup complete');
    </script>
</body>
</html>
